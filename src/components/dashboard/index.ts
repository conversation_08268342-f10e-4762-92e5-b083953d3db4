// Dashboard Components Barrel Export
// This file provides a centralized export for all dashboard components

// Layout Components
export {
  ResponsiveDashboardLayout,
  useDashboardLayout,
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveCard,
  ResponsiveHeader,
  ResponsiveStats,
} from "./responsive-layout";
export { ResponsiveSidebar } from "./responsive-sidebar";
// export { Sidebar } from "./Sidebar"; // Commented out - using ResponsiveSidebar instead

// Management Components
export { NotificationsManager } from "./notifications-manager";
// export { ProjectTypesManager } from "./project-types-manager"; // TODO: Implement

// Analytics Components
// export { AnalyticsOverview } from "./analytics-overview"; // TODO: Implement
// export { PerformanceMetrics } from "./performance-metrics"; // TODO: Implement

// Content Management
// export { ContentEditor } from "./content-editor"; // TODO: Implement
// export { MediaLibrary } from "./media-library"; // TODO: Implement

// User Management
// export { UserProfile } from "./user-profile"; // TODO: Implement
// export { ActivityLog } from "./activity-log"; // TODO: Implement

// Settings Components
// export { DashboardSettings } from "./dashboard-settings"; // TODO: Implement
// export { SecuritySettings } from "./security-settings"; // TODO: Implement

// Types
// export type { NotificationsManagerProps } from "./notifications-manager"; // TODO: Add proper types
// export type { ProjectTypesManagerProps } from "./project-types-manager"; // TODO: Implement
