"use client";

import {
  ThemeProvider as NextThemesProvider,
  type ThemeProviderProps,
} from "next-themes";
import * as React from "react";

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div style={{ visibility: "hidden" }}>{children}</div>;
  }

  return (
    <NextThemesProvider
      {...props}
      defaultTheme="dark"
      forcedTheme="dark"
      enableSystem={false}
      attribute="class"
      value={{ dark: "dark" }}
    >
      {children}
    </NextThemesProvider>
  );
}
