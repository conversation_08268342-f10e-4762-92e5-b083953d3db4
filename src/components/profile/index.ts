// Profile Components Barrel Export
// This file provides a centralized export for all profile components

// Profile Management
export { ProfilePhotoManager } from "./profile-photo-manager";
export { SmartSocialLinks } from "./smart-social-links";
// export { ProfileEditor } from "./profile-editor"; // TODO: Implement
// export { ProfilePreview } from "./profile-preview"; // TODO: Implement

// Social Components
// export { SocialLinksDisplay } from "./social-links-display"; // TODO: Implement
// export { SocialLinksEditor } from "./social-links-editor"; // TODO: Implement

// Skills & Experience
// export { SkillsManager } from "./skills-manager"; // TODO: Implement
// export { ExperienceTimeline } from "./experience-timeline"; // TODO: Implement
// export { EducationHistory } from "./education-history"; // TODO: Implement

// Portfolio Components
// export { PortfolioGallery } from "./portfolio-gallery"; // TODO: Implement
// export { ProjectShowcase } from "./project-showcase"; // TODO: Implement
// export { RecommendationsDisplay } from "./recommendations-display"; // TODO: Implement

// Contact & Communication
// export { ContactInformation } from "./contact-information"; // TODO: Implement
// export { AvailabilityStatus } from "./availability-status"; // TODO: Implement
// export { ContactPreferences } from "./contact-preferences"; // TODO: Implement

// Types
// export type { ProfilePhotoManagerProps } from "./profile-photo-manager"; // TODO: Add proper types
// export type { SmartSocialLinksProps } from "./smart-social-links"; // TODO: Add proper types
// export type { SocialLink, SocialPlatform } from "./smart-social-links"; // TODO: Add proper types
