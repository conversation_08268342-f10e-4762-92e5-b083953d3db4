// Projects Components Barrel Export
// This file provides a centralized export for all project components

// Enhanced Project Management (New)
export { EnhancedProjectForm } from "./enhanced-project-form";
export { EnhancedProjectsDashboard } from "./enhanced-projects-dashboard";

// Legacy Project Display
// export { ProjectsList } from "./projects-list"; // TODO: Implement
// export { ProjectCard } from "./project-card"; // TODO: Implement
// export { ProjectGrid } from "./project-grid"; // TODO: Implement
// export { ProjectDetails } from "./project-details"; // TODO: Implement

// Project Management
// export { ProjectEditor } from "./project-editor"; // TODO: Implement
// export { ProjectFilters } from "./project-filters"; // TODO: Implement
// export { ProjectSearch } from "./project-search"; // TODO: Implement
// export { ProjectSorting } from "./project-sorting"; // TODO: Implement

// Project Features
// export { ProjectGallery } from "./project-gallery"; // TODO: Implement
// export { ProjectTechnologies } from "./project-technologies"; // TODO: Implement
// export { ProjectTimeline } from "./project-timeline"; // TODO: Implement
// export { ProjectMetrics } from "./project-metrics"; // TODO: Implement

// Project Actions
// export { ProjectActions } from "./project-actions"; // TODO: Implement
// export { ProjectExport } from "./project-export"; // TODO: Implement
// export { ProjectShare } from "./project-share"; // TODO: Implement
// export { ProjectArchive } from "./project-archive"; // TODO: Implement

// Types
// export type { Project, ProjectType, ProjectStatus } from "./types"; // TODO: Implement
// export type { ProjectsListProps } from "./projects-list"; // TODO: Implement
// export type { ProjectCardProps } from "./project-card"; // TODO: Implement
// export type { ProjectFiltersProps } from "./project-filters"; // TODO: Implement
