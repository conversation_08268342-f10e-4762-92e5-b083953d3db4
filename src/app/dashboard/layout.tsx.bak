"use client";

import { useState, useEffect } from "react";
import type React from "react";
import { useRouter } from "next/navigation";
import { DashboardHeader } from "@/components/dashboard/header";
import { Toaster } from "@/components/ui/toaster";
import { ThemeProvider } from "@/components/theme-provider";
import { AuthProvider } from "@/contexts/auth-context";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { DashboardSidebar } from "@/components/dashboard/sidebar";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  // Set isClient to true when component mounts (client-side only)
  useEffect(() => {
    setIsClient(true);

    // Check for auth tokens
    if (typeof window !== "undefined") {
      const hasAuthToken = document.cookie.includes("auth-token=");
      const hasClientToken = document.cookie.includes("client-auth-token=");

      // If no tokens, redirect to login
      if (!hasAuthToken && !hasClientToken) {
        router.push("/auth/login");
      }
    }
  }, [router]);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Show loading state until client-side code runs
  if (!isClient) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-12 h-12 border-4 border-t-blue-500 border-b-blue-700 rounded-full animate-spin"></div>
          <p className="text-lg font-medium">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <AuthProvider>
        <ProtectedRoute>
          <div className="flex min-h-screen">
            {/* Use CSS display property instead of conditional rendering */}
            <div style={{ display: sidebarOpen ? 'block' : 'none' }}>
              <DashboardSidebar />
            </div>
            <div className="flex-1 flex flex-col">
              <DashboardHeader toggleSidebar={toggleSidebar} />
              <div className="flex-1 p-6 overflow-auto">{children}</div>
              <Toaster />
            </div>
          </div>
        </ProtectedRoute>
      </AuthProvider>
    </ThemeProvider>
  );
}
