import { projects } from "./Links";

const getFaviconUrl = (url: string | undefined): string => {
  if (!url) return "/app/images/logo.png";
  try {
    const domain = new URL(url).origin;
    return `${domain}/favicon.ico`;
  } catch {
    return "/app/images/logo.png";
  }
};

export interface Project {
  _id: string;
  title: string;
  description: string;
  projectType: string;
  images: string[];
  videoUrl?: string;
  githubUrl?: string;
  websiteUrl?: string;
  technologies: string[];
  featured: boolean;
  status: "Draft" | "Published" | "Archived";
  createdAt: string;
  updatedAt: string;
}

export const projectsData: Project[] = [
  {
    _id: "proj_1",
    title: "Samtax - Tax Management System",
    description:
      "A comprehensive tax management system built with React and Node.js. Features include client management, tax calculations, document uploads, and automated reporting.",
    projectType: "Web Application",
    images: [],
    websiteUrl: projects.samtax,
    githubUrl: "",
    technologies: [
      "React",
      "TypeScript",
      "Node.js",
      "MongoDB",
      "Tailwind CSS",
      "Shadcn UI",
    ],
    featured: true,
    status: "Published",
    createdAt: "2023-07-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  },
  {
    _id: "proj_2",
    title: "Gradients CSS - CSS Gradient Generator",
    description:
      "A modern CSS gradient generator tool that allows developers to create beautiful gradients with real-time preview and copy-to-clipboard functionality.",
    projectType: "Tool",
    images: [],
    websiteUrl: projects.gradientscss.website,
    githubUrl: projects.gradientscss.github,
    technologies: ["React", "TypeScript", "Tailwind CSS", "Vite"],
    featured: true,
    status: "Published",
    createdAt: "2023-03-01T00:00:00.000Z",
    updatedAt: "2023-06-01T00:00:00.000Z",
  },
  {
    _id: "proj_3",
    title: "Raouf Zadi Portfolio",
    description:
      "A professional portfolio website for a designer, featuring responsive design, smooth animations, and optimized performance.",
    projectType: "Frontend",
    images: [],
    websiteUrl: projects.raoufzadi,
    githubUrl: "",
    technologies: ["React", "TypeScript", "Tailwind CSS", "Framer Motion"],
    featured: false,
    status: "Published",
    createdAt: "2022-11-01T00:00:00.000Z",
    updatedAt: "2023-01-01T00:00:00.000Z",
  },
  {
    _id: "proj_4",
    title: "Naj Training Center",
    description:
      "A training center website with course management, student enrollment, and progress tracking features.",
    projectType: "Web Application",
    images: [],
    websiteUrl: projects.najcenter,
    githubUrl: "",
    technologies: ["React", "JavaScript", "Material-UI", "Node.js"],
    featured: false,
    status: "Published",
    createdAt: "2022-08-01T00:00:00.000Z",
    updatedAt: "2022-12-01T00:00:00.000Z",
  },
  {
    _id: "proj_5",
    title: "Rove - Travel Planning App",
    description:
      "A travel planning application built with React frontend and Laravel backend, featuring trip planning, booking management, and user reviews.",
    projectType: "Web Application",
    images: [],
    websiteUrl: "",
    githubUrl: projects.rove,
    technologies: ["React", "Tailwind CSS", "Laravel", "MySQL", "PHP"],
    featured: false,
    status: "Published",
    createdAt: "2022-05-01T00:00:00.000Z",
    updatedAt: "2022-09-01T00:00:00.000Z",
  },
  {
    _id: "proj_6",
    title: "Sustainable Star Website",
    description:
      "A corporate website for a sustainability consulting company, featuring service showcases, case studies, and contact forms.",
    projectType: "Frontend",
    images: [],
    websiteUrl: projects.sustainablestar,
    githubUrl: "",
    technologies: ["React", "Tailwind CSS", "Material-UI", "Framer Motion"],
    featured: false,
    status: "Published",
    createdAt: "2022-03-01T00:00:00.000Z",
    updatedAt: "2022-07-01T00:00:00.000Z",
  },
  {
    _id: "proj_7",
    title: "Bookstore API",
    description:
      "A RESTful API for a bookstore management system with features for book inventory, user management, orders, and reviews.",
    projectType: "API",
    images: [],
    websiteUrl: "",
    githubUrl: projects.bookstoreapi,
    technologies: ["Node.js", "Express.js", "MongoDB", "Mongoose", "JWT"],
    featured: false,
    status: "Published",
    createdAt: "2021-12-01T00:00:00.000Z",
    updatedAt: "2022-02-01T00:00:00.000Z",
  },
];

// Legacy data for backward compatibility
export const ProjectsData = [
  {
    id: "samtax",
    titleKey: "projects.samtax.title",
    descriptionKey: "projects.samtax.description",
    skills: ["React", "Tailwind CSS", "Shadcn UI"],
    links: {
      website: projects.samtax,
    },
    img: getFaviconUrl(projects.samtax),
  },
  {
    id: "gradients-css",
    titleKey: "projects.gradientsCss.title",
    descriptionKey: "projects.gradientsCss.description",
    skills: [
      "React JS",
      "Typescript",
      "Tailwind CSS",
      "Github",
      "Git",
      "RESTful APIs",
    ],
    links: {
      website: projects.gradientscss.website,
      github: projects.gradientscss.github,
    },
    img: getFaviconUrl(projects.gradientscss.website),
  },
  {
    id: "raouf-zadi",
    titleKey: "projects.raoufzadi.title",
    descriptionKey: "projects.raoufzadi.description",
    skills: ["React JS", "Typescript", "Tailwind CSS", "Github", "Git"],
    links: {
      website: projects.raoufzadi,
    },
    img: getFaviconUrl(projects.raoufzadi),
  },
  {
    id: "naj-training-center",
    titleKey: "projects.najTrainingCenter.title",
    descriptionKey: "projects.najTrainingCenter.description",
    skills: ["React JS", "Javascript", "MIUI"],
    links: {
      website: projects.najcenter,
    },
    img: getFaviconUrl(projects.najcenter),
  },
  {
    id: "rove",
    titleKey: "projects.rove.title",
    descriptionKey: "projects.rove.description",
    skills: ["React", "Tailwind CSS", "Laravel", "MYSQL"],
    links: {
      github: projects.rove,
    },
    img: getFaviconUrl(undefined),
  },
  {
    id: "sustainable-star",
    titleKey: "projects.sustainableStar.title",
    descriptionKey: "projects.sustainableStar.description",
    skills: ["React", "Tailwind CSS", "Material UI"],
    links: {
      website: projects.sustainablestar,
    },
    img: getFaviconUrl(projects.sustainablestar),
  },
  {
    id: "bookstore-api",
    titleKey: "projects.bookstoreApi.title",
    descriptionKey: "projects.bookstoreApi.description",
    skills: ["Node JS", "Express JS", "Mongoose DB"],
    links: {
      github: projects.bookstoreapi,
    },
    img: getFaviconUrl(undefined),
  },
];
