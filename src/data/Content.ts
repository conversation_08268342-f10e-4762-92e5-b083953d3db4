export interface Content {
  _id: string;
  section: "hero" | "footer" | "about";
  title: string;
  subtitle: string;
  description: string;
  content: any;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export const contentData: Content[] = [
  {
    _id: "content_hero",
    section: "hero",
    title: "Full Stack Developer",
    subtitle: "Building Digital Solutions",
    description: "I'm <PERSON><PERSON>, a passionate Full Stack Developer specializing in React, Node.js, and modern web technologies. I create impactful web applications that solve real-world problems.",
    content: {
      greeting: "Hello, I'm",
      name: "<PERSON><PERSON>",
      tagline: "Full Stack Developer",
      description: "I'm a passionate Full Stack Developer with expertise in React, Node.js, TypeScript, and modern web technologies. I love creating impactful web applications that solve real-world problems and provide excellent user experiences.",
      cta: {
        primary: "View My Work",
        secondary: "Get In Touch"
      }
    },
    isActive: true,
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z"
  },
  {
    _id: "content_about",
    section: "about",
    title: "About Me",
    subtitle: "Passionate Developer",
    description: "I'm a dedicated Full Stack Developer with a strong foundation in computer science and years of hands-on experience building web applications.",
    content: {
      introduction: "I'm a dedicated Full Stack Developer with a strong foundation in computer science and years of hands-on experience building web applications.",
      passion: "I'm passionate about creating clean, efficient code and delivering exceptional user experiences.",
      approach: "My approach combines technical expertise with creative problem-solving to build solutions that make a real impact.",
      values: [
        "Clean, maintainable code",
        "User-centered design",
        "Continuous learning",
        "Collaborative teamwork"
      ]
    },
    isActive: true,
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z"
  },
  {
    _id: "content_footer",
    section: "footer",
    title: "Let's Connect",
    subtitle: "Get In Touch",
    description: "Feel free to reach out for collaborations, opportunities, or just to say hello!",
    content: {
      message: "Feel free to reach out for collaborations, opportunities, or just to say hello!",
      copyright: "© 2024 Baraa Alshaer. All rights reserved.",
      links: {
        privacy: "/privacy",
        terms: "/terms"
      }
    },
    isActive: true,
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z"
  }
];
