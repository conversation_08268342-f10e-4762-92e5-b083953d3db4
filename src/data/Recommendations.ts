export interface Recommendation {
  _id: string;
  name: string;
  position: string;
  company: string;
  text: string;
  relationship: "Client" | "Colleague" | "Manager" | "Other";
  avatar?: string;
  featured: boolean;
  date: string;
  createdAt: string;
  updatedAt: string;
}

export const recommendationsData: Recommendation[] = [
  {
    _id: "rec_1",
    name: "<PERSON>",
    position: "Project Manager",
    company: "Samtax",
    text: "<PERSON><PERSON> is an exceptional developer who consistently delivers high-quality work. His expertise in React and Node.js helped us build a robust tax management system. He's proactive, communicates well, and always meets deadlines. I highly recommend him for any full-stack development project.",
    relationship: "Manager",
    avatar: "",
    featured: true,
    date: "2024-01-15T00:00:00.000Z",
    createdAt: "2024-01-15T00:00:00.000Z",
    updatedAt: "2024-01-15T00:00:00.000Z"
  },
  {
    _id: "rec_2",
    name: "<PERSON>",
    position: "UI/UX Designer",
    company: "Sustainable Star",
    text: "Working with <PERSON><PERSON> was a pleasure. He perfectly translated our designs into responsive, interactive web applications. His attention to detail and ability to implement complex animations made our projects stand out. He's reliable and has great problem-solving skills.",
    relationship: "Colleague",
    avatar: "",
    featured: true,
    date: "2023-06-20T00:00:00.000Z",
    createdAt: "2023-06-20T00:00:00.000Z",
    updatedAt: "2023-06-20T00:00:00.000Z"
  },
  {
    _id: "rec_3",
    name: "<PERSON> Khalil",
    position: "CEO",
    company: "Tech Solutions",
    text: "Baraa developed our company website and exceeded our expectations. The site is fast, modern, and perfectly represents our brand. He was professional throughout the project and provided excellent post-launch support. We'll definitely work with him again.",
    relationship: "Client",
    avatar: "",
    featured: true,
    date: "2023-03-10T00:00:00.000Z",
    createdAt: "2023-03-10T00:00:00.000Z",
    updatedAt: "2023-03-10T00:00:00.000Z"
  }
];
