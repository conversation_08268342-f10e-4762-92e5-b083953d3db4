export interface Profile {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  bio?: string;
  avatar?: string;
  skills: string[];
  updatedAt: string;
}

export const profileData: Profile = {
  _id: "profile_1",
  firstName: "Baraa",
  lastName: "<PERSON><PERSON><PERSON>",
  email: "<EMAIL>",
  phone: "970599349034",
  location: "Gaza, Palestine",
  bio: "Passionate Full Stack Developer with expertise in React, Node.js, TypeScript, and modern web technologies. I love creating impactful web applications that solve real-world problems and provide excellent user experiences.",
  avatar: "https://cdn.dribbble.com/userupload/14186516/file/original-302bcec5d5a7d2bae6c18ee8cabc5f37.png?resize=400x400",
  skills: [
    "React",
    "TypeScript",
    "JavaScript",
    "Node.js",
    "Next.js",
    "Express.js",
    "MongoDB",
    "PostgreSQL",
    "Tailwind CSS",
    "CSS3",
    "HTML5",
    "Git",
    "Docker",
    "REST APIs",
    "GraphQL",
    "AWS",
    "Vercel",
    "Figma"
  ],
  updatedAt: "2024-01-01T00:00:00.000Z"
};
